import cv2
import numpy as np
import pya<PERSON><PERSON><PERSON>

def screen_capture_loop():
    # Create a named window and set its size
    cv2.namedWindow("Screen Capture", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Screen Capture", 1200, 800)
    
    # Calculate the region to capture (middle of 1920x1080)
    screen_width, screen_height = 1920, 1080
    capture_width, capture_height = 1250, 850
    
    # Calculate the starting position for centered capture
    start_x = (screen_width - capture_width) // 2
    start_y = (screen_height - capture_height) // 2
    
    while True:
        # Capture only the middle region
        screenshot = pyautogui.screenshot(region=(start_x, start_y, capture_width, capture_height))
        frame = np.array(screenshot)
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        
        cv2.imshow("Screen Capture", frame)
        
        if cv2.waitKey(1) & 0xFF == ord("q"):
            break
    
    cv2.destroyAllWindows()

if __name__ == "__main__":
    screen_capture_loop()










