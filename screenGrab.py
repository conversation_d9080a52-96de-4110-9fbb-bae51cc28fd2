import cv2
import numpy as np
import pyautogui
import pytesseract

def detect_circles(frame):
	gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
	gray = cv2.medianBlur(gray, 5)

	circles = cv2.HoughCircles(
		gray,
		cv2.HOUGH_GRADIENT,
		dp=1,
		minDist=30,
		param1=50,
		param2=30,
		minRadius=10,
		maxRadius=100
	)

	if circles is not None:
		circles = np.round(circles[0, :]).astype("int")
		for (x, y, r) in circles:
			cv2.circle(frame, (x, y), r, (0, 255, 0), 2)
			cv2.circle(frame, (x, y), 2, (0, 0, 255), 3)

	return frame

def detect_text(frame):
	try:
		gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

		data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)

		for i in range(len(data["text"])):
			if int(data["conf"][i]) > 30:
				text = data["text"][i].strip()
				if text:
					x, y, w, h = data["left"][i], data["top"][i], data["width"][i], data["height"][i]
					cv2.rectangle(frame, (x, y), (x + w, y + h), (255, 0, 0), 2)
					cv2.putText(frame, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
	except Exception as e:
		print(f"Error in text detection: {e}")

	return frame

def screen_capture_loop():
    # Create a named window and set its size
    cv2.namedWindow("Screen Capture", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Screen Capture", 1200, 800)
    
    # Calculate the region to capture (middle of 1920x1080)
    screen_width, screen_height = 1920, 1080
    capture_width, capture_height = 1250, 850
    
    # Calculate the starting position for centered capture
    start_x = (screen_width - capture_width) // 2
    start_y = (screen_height - capture_height) // 2
    
    while True:
        # Capture only the middle region
        screenshot = pyautogui.screenshot(region=(start_x, start_y, capture_width, capture_height))
        frame = np.array(screenshot)
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

        frame = detect_circles(frame)
        frame = detect_text(frame)

        cv2.imshow("Screen Capture", frame)
        
        if cv2.waitKey(1) & 0xFF == ord("q"):
            break
    
    cv2.destroyAllWindows()

if __name__ == "__main__":
    screen_capture_loop()










